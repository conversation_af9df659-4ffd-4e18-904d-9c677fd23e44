{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "configMigration": true, "customManagers": [{"description": "Handle updates to Docker images via pull-through cache in the experience-build account.", "customType": "regex", "datasourceTemplate": "docker", "depNameTemplate": "************.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/{{repo}}", "managerFilePatterns": ["/Dockerfile(-.*)?$/"], "packageNameTemplate": "public.ecr.aws/{{repo}}", "matchStrings": ["FROM ************\\.dkr\\.ecr\\.eu-west-1\\.amazonaws\\.com\\/ecr-public\\/(?<repo>[^:]+):(?<currentValue>[^@ ]+)(@(?<currentDigest>sha256:[0-9a-f]{64}))?(\\s+[Aa][Ss]\\s+\\w+)?\\n"]}, {"description": "Handle updates to the version of Go.", "customType": "regex", "datasourceTemplate": "golang-version", "depNameTemplate": "golang", "managerFilePatterns": ["/go.mod/", "/golang-install.sh/"], "matchStrings": ["^go (?<currentValue>.*?)\\n", "\\sgo (?<currentValue>.*?)\\n", "GOLANG_21_VERSION=\"(?<currentValue>.*?)\"\\n"]}, {"description": "Handle updates to the version of Renovate itself.", "customType": "regex", "managerFilePatterns": ["/renovate.yml/"], "matchStrings": ["RENOVATE_VERSION:\\s*['\"]?(?<currentValue>\\d+\\.\\d+\\.\\d+[^'\"\\s]*)['\"]?\\s*$"], "depNameTemplate": "renovate", "packageNameTemplate": "renovate/renovate", "datasourceTemplate": "docker", "versioningTemplate": "semver", "extractVersionTemplate": "^(\\d+\\.\\d+\\.\\d+.*)$"}], "extends": ["config:recommended", ":automergeDigest", ":automergeMinor", ":disableRateLimiting", ":maintainLockFilesMonthly", ":pinVersions", ":rebaseStalePrs", ":semanticCommits", ":timezone(Europe/London)", "docker:disable<PERSON><PERSON><PERSON>", "docker:pinDigests", "helpers:pinGitHubActionDigests"], "ignorePaths": ["**/node_modules/**", "**/bower_components/**", "**/vendor/**", "**/examples/**", "**/__tests__/**", "**/tests/**", "**/__fixtures__/**"], "labels": ["renovate"], "nvm": {"enabled": false}, "packageRules": [{"description": "Group Docker digest updates together.", "groupName": "digest dependencies (docker)", "groupSlug": "digest-docker", "matchManagers": ["docker-compose", "dockerfile"], "matchUpdateTypes": ["digest"]}, {"description": "Group non-major GitHub Actions updates together.", "groupName": "non-major dependencies (github-actions)", "groupSlug": "non-major-github-actions", "matchManagers": ["github-actions"], "matchUpdateTypes": ["digest", "patch", "minor"]}, {"description": "Group non-major NPM updates together.", "groupName": "non-major dependencies (js)", "groupSlug": "non-major-js", "matchManagers": ["npm"], "matchUpdateTypes": ["digest", "patch", "minor"], "matchPackageNames": ["!@golevelup/ts-jest", "!@headlessui/{/,}**", "!@next/mdx", "!@nx/{/,}**", "!@opentelemetry/{/,}**", "!@sentry/{/,}**", "!eslint-plugin-prefer-arrow-functions", "!mimetext", "!next", "!node", "!react-hook-form", "!reflect-metadata", "!stripe"]}, {"description": "Group non-major Go updates together.", "groupName": "non-major dependencies (go)", "groupSlug": "non-major-go", "matchManagers": ["gomod"], "matchUpdateTypes": ["digest", "patch", "minor"], "postUpgradeTasks": {"commands": ["go mod tidy"], "fileFilters": ["**/*.go", "**/*.sum", "**/*.mod"], "executionMode": "branch"}}, {"description": "Group major AWS SDK Client mock updates together.", "groupName": "aws-sdk-client-mock", "matchPackageNames": ["aws-sdk-client-mock", "aws-sdk-client-mock-jest"], "matchUpdateTypes": "major"}, {"description": "Group major OpenTelemetry updates together.", "groupName": "@opentelemetry", "matchPackageNames": ["@opentelemetry/{/,}**"], "matchUpdateTypes": "major"}, {"description": "Group major Smithy updates together.", "groupName": "@smithy", "matchPackageNames": ["@smithy/{/,}**"], "matchUpdateTypes": "major"}, {"description": "Update .nvmrc file when Node.js version is updated.", "matchDepNames": ["node"], "postUpgradeTasks": {"commands": ["echo {{{new<PERSON><PERSON><PERSON>}}} > .nvmrc"], "executionMode": "branch", "fileFilters": [".nvmrc"]}}, {"description": "Disable updates to pull through cache images. A custom regex manager handles these updates.", "enabled": false, "matchDatasources": ["docker"], "matchDepNames": ["************.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/alpine", "************.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/golang", "************.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node"], "matchManagers": ["docker-compose", "dockerfile"]}, {"description": "Disable updates to MySQL and Postgres. These need to be manually kept in sync with AWS environments.", "enabled": false, "matchDatasources": ["docker"], "matchDepNames": ["mysql", "postgres"], "matchManagers": ["docker-compose", "dockerfile"]}, {"description": "Disable updates to Pod Point Platform actions. We have not configured the necessary authentication to support these.", "enabled": false, "matchDatasources": ["github-tags"], "matchDepNames": ["Pod-Point-Platform/build-publish-ecr", "Pod-Point-Platform/deploy-image-uri-to-ecs", "Pod-Point-Platform/run-ecs-task"]}, {"description": "Disable updates to Repo Visualizer. This has a non-standard release name convention which causes Renovate warnings.", "enabled": false, "matchDatasources": ["github-tags"], "matchDepNames": ["githubocto/repo-visualizer"]}, {"description": "Disable all updates to NPM packages which are handled by nx migrate.", "enabled": false, "matchManagers": ["npm"], "matchPackageNames": ["@nx/{/,}**", "nx"]}, {"description": "Disable major updates to NPM packages which are handled manually, or by nx migrate.", "enabled": false, "matchManagers": ["npm"], "matchPackageNames": ["@chromatic-com/storybook", "@maizzle/{/,}**", "@nestjs/{/,}**", "@next/mdx", "@storybook/{/,}**", "@types/react", "@types/react-dom", "@typescript-eslint/{/,}**", "cypress", "eslint", "eslint-config-next", "eslint-config-prettier", "eslint-plugin-cypress", "eslint-plugin-storybook", "eslint-plugin-unicorn", "eslint-plugin-unused-imports", "nestjs-cls", "next", "prettier", "react", "react-dom", "storybook", "typescript"], "matchUpdateTypes": ["major"]}, {"description": "Disable updates to Go version. A custom regex manager handles these updates.", "enabled": false, "matchManagers": ["gomod"], "matchPackageNames": ["go{/,}**"]}], "prConcurrentLimit": 1, "prHourlyLimit": 0, "pub": {"enabled": false}, "rebaseWhen": "conflicted", "schedule": ["after 8am and before 12pm every weekday"]}