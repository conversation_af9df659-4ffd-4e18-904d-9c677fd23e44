import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  Configuration,
  DriverChargesApi,
  DriversApi,
  OrganisationChargesApi,
} from '@experience/shared/axios/data-platform-api-client';
import { DriverAutoExpensesCommand } from './commands/driver-auto-expenses.command';
import { DriverModule } from '@experience/commercial/site-admin/nest/driver-module';
import { ExpenseController } from './expense.controller';
import { ExpenseService } from './expense.service';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { Users } from '@experience/shared/sequelize/podadmin';

@Module({
  imports: [AdminModule, ConfigModule, DriverModule],
  controllers: [ExpenseController],
  providers: [
    DriverAutoExpensesCommand,
    ExpenseService,
    {
      provide: 'USERS_REPOSITORY',
      useValue: Users,
    },
    {
      inject: [ConfigService],
      provide: OrganisationChargesApi,
      useFactory: async (configService: ConfigService) =>
        new OrganisationChargesApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: DriverChargesApi,
      useFactory: async (configService: ConfigService) =>
        new DriverChargesApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: DriversApi,
      useFactory: async (configService: ConfigService) =>
        new DriversApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class ExpenseModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(ExpenseController);
  }
}
