import { AdditionalInfoController } from './additional-info/additional-info.controller';
import { AdditionalInfoService } from './additional-info/additional-info.service';
import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import { ChargeModule } from '@experience/commercial/site-admin/nest/charge-module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  Configuration,
  ProjectionGroupStatisticsApi,
} from '@experience/shared/axios/data-platform-api-client';
import { ContactDetailsController } from './contact-details/contact-details.controller';
import { ContactDetailsService } from './contact-details/contact-details.service';
import { DriverModule } from '@experience/commercial/site-admin/nest/driver-module';
import { EnergyCostController } from './energy-cost/energy-cost.controller';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { OpeningTimesController } from './opening-times/opening-times.controller';
import { OpeningTimesService } from './opening-times/opening-times.service';
import {
  ParkingOpeningTimeNotes,
  ParkingOpeningTimes,
  PodAddresses,
  PodadminSequelizeModule,
  TariffTiers,
  Tariffs,
} from '@experience/shared/sequelize/podadmin';
import { SiteController } from './site.controller';
import { SiteService } from './site.service';
import { StatementController } from './statement/statement.controller';
import { StatementService } from './statement/statement.service';
import { StatsController } from './stats/stats.controller';
import { StatsService } from './stats/stats.service';
import EnergyCostService from './energy-cost/energy-cost.service';

@Module({
  imports: [
    AdminModule,
    ChargeModule,
    ConfigModule,
    DriverModule,
    PodadminSequelizeModule,
  ],
  controllers: [
    AdditionalInfoController,
    ContactDetailsController,
    EnergyCostController,
    OpeningTimesController,
    SiteController,
    StatementController,
    StatsController,
  ],
  providers: [
    AdditionalInfoService,
    ContactDetailsService,
    EnergyCostService,
    OpeningTimesService,
    SiteService,
    StatementService,
    StatsService,
    {
      provide: 'POD_ADDRESSES_REPOSITORY',
      useValue: PodAddresses,
    },
    {
      provide: 'OPENING_TIMES_REPOSITORY',
      useValue: ParkingOpeningTimes,
    },
    {
      provide: 'OPENING_TIME_NOTES_REPOSITORY',
      useValue: ParkingOpeningTimeNotes,
    },
    {
      provide: 'TARIFF_REPOSITORY',
      useValue: Tariffs,
    },
    {
      provide: 'TARIFF_TIER_REPOSITORY',
      useValue: TariffTiers,
    },
    {
      inject: [ConfigService],
      provide: ProjectionGroupStatisticsApi,
      useFactory: async (configService: ConfigService) =>
        new ProjectionGroupStatisticsApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
  ],
  exports: [SiteService],
})
export class SiteModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminMiddleware)
      .forRoutes(
        AdditionalInfoController,
        ContactDetailsController,
        EnergyCostController,
        OpeningTimesController,
        SiteController,
        StatementController,
        StatsController
      );
  }
}
