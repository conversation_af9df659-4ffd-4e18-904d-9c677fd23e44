import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import {
  ChargeStatisticsApi,
  Configuration,
  UsageApi,
} from '@experience/shared/axios/data-platform-api-client';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { InsightsController } from './insights.controller';
import { InsightsService } from './insights.service';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { PodModule } from '@experience/commercial/site-admin/nest/pod-module';
import { PodadminSequelizeModule } from '@experience/shared/sequelize/podadmin';
import { SiteModule } from '@experience/commercial/site-admin/nest/site-module';

@Module({
  imports: [
    ConfigModule,
    AdminModule,
    PodModule,
    SiteModule,
    PodadminSequelizeModule,
  ],
  controllers: [InsightsController],
  providers: [
    InsightsService,
    {
      inject: [ConfigService],
      provide: ChargeStatisticsApi,
      useFactory: async (configService: ConfigService) =>
        new ChargeStatisticsApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    {
      inject: [ConfigService],
      provide: UsageApi,
      useFactory: async (configService: ConfigService) =>
        new UsageApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class InsightsModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(InsightsController);
  }
}
